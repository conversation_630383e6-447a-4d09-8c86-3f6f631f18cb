# 简易网络优化脚本 - 防闪退版本
# 适用于快客VPN + 软路由环境

# 设置错误处理，防止脚本意外退出
$ErrorActionPreference = "SilentlyContinue"
$Host.UI.RawUI.WindowTitle = "简易网络优化工具"

# 清屏并显示标题
Clear-Host
Write-Host ""
Write-Host "================================================================" -ForegroundColor Green
Write-Host "                   简易网络优化工具 v1.0" -ForegroundColor Green  
Write-Host "                 快客VPN + 软路由专用版本" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Green
Write-Host ""

# 暂停函数
function Pause-Script {
    param([string]$Message = "按任意键继续...")
    Write-Host $Message -ForegroundColor Yellow
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}

# 检查管理员权限
Write-Host "正在检查管理员权限..." -ForegroundColor Cyan
try {
    $isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
    
    if (-not $isAdmin) {
        Write-Host ""
        Write-Host "================================================================" -ForegroundColor Red
        Write-Host "                        权限不足！" -ForegroundColor Red
        Write-Host "================================================================" -ForegroundColor Red
        Write-Host ""
        Write-Host "请按以下步骤重新运行：" -ForegroundColor Yellow
        Write-Host "1. 按 Win + X 键" -ForegroundColor White
        Write-Host "2. 选择 'Windows PowerShell (管理员)'" -ForegroundColor White
        Write-Host "3. 在弹出的窗口中重新运行此脚本" -ForegroundColor White
        Write-Host ""
        Pause-Script "按任意键退出..."
        exit 1
    }
    
    Write-Host "✓ 管理员权限检查通过" -ForegroundColor Green
} catch {
    Write-Host "✗ 权限检查失败: $($_.Exception.Message)" -ForegroundColor Red
    Pause-Script
    exit 1
}

Write-Host ""

# 主菜单函数
function Show-Menu {
    Clear-Host
    Write-Host ""
    Write-Host "================================================================" -ForegroundColor Cyan
    Write-Host "                      选择优化模式" -ForegroundColor Cyan
    Write-Host "================================================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "  [1] 基础优化 - 清理DNS和网络缓存" -ForegroundColor White
    Write-Host "  [2] VPN优化 - 解决快客VPN连接问题" -ForegroundColor White  
    Write-Host "  [3] 软路由优化 - 优化软路由环境" -ForegroundColor White
    Write-Host "  [4] 完整优化 - 执行所有优化项目" -ForegroundColor White
    Write-Host "  [5] 网络诊断 - 检查网络状态" -ForegroundColor White
    Write-Host "  [0] 退出程序" -ForegroundColor White
    Write-Host ""
    Write-Host "================================================================" -ForegroundColor Cyan
    Write-Host ""
}

# 基础优化函数
function Start-BasicOptimization {
    Write-Host "开始基础网络优化..." -ForegroundColor Green
    Write-Host ""
    
    try {
        Write-Host "[1/5] 清理DNS缓存..." -ForegroundColor Cyan
        Clear-DnsClientCache
        ipconfig /flushdns | Out-Null
        Write-Host "      ✓ DNS缓存清理完成" -ForegroundColor Green
        
        Write-Host "[2/5] 重置网络堆栈..." -ForegroundColor Cyan
        netsh winsock reset | Out-Null
        netsh int ip reset | Out-Null
        Write-Host "      ✓ 网络堆栈重置完成" -ForegroundColor Green
        
        Write-Host "[3/5] 优化TCP设置..." -ForegroundColor Cyan
        netsh int tcp set global autotuninglevel=normal | Out-Null
        netsh int tcp set global chimney=enabled | Out-Null
        netsh int tcp set global rss=enabled | Out-Null
        Write-Host "      ✓ TCP设置优化完成" -ForegroundColor Green
        
        Write-Host "[4/5] 配置DNS服务器..." -ForegroundColor Cyan
        $adapters = Get-NetAdapter | Where-Object {$_.Status -eq "Up"}
        foreach ($adapter in $adapters) {
            try {
                Set-DnsClientServerAddress -InterfaceAlias $adapter.Name -ServerAddresses @("*******", "*******") -ErrorAction SilentlyContinue
            } catch {
                # 忽略错误，继续下一个适配器
            }
        }
        Write-Host "      ✓ DNS服务器配置完成" -ForegroundColor Green
        
        Write-Host "[5/5] 重启网络服务..." -ForegroundColor Cyan
        Restart-Service -Name "Dhcp" -Force -ErrorAction SilentlyContinue
        Restart-Service -Name "Dnscache" -Force -ErrorAction SilentlyContinue
        Write-Host "      ✓ 网络服务重启完成" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "================================================================" -ForegroundColor Green
        Write-Host "                    基础优化完成！" -ForegroundColor Green
        Write-Host "================================================================" -ForegroundColor Green
        
    } catch {
        Write-Host "      ✗ 优化过程中出现错误: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# VPN优化函数
function Start-VPNOptimization {
    Write-Host "开始VPN专用优化..." -ForegroundColor Green
    Write-Host ""
    
    try {
        Write-Host "[1/6] 停止冲突服务..." -ForegroundColor Cyan
        Stop-Service -Name "WinHttpAutoProxySvc" -Force -ErrorAction SilentlyContinue
        Write-Host "      ✓ 冲突服务已停止" -ForegroundColor Green
        
        Write-Host "[2/6] 清理网络配置..." -ForegroundColor Cyan
        ipconfig /release | Out-Null
        ipconfig /flushdns | Out-Null  
        ipconfig /renew | Out-Null
        Write-Host "      ✓ 网络配置已清理" -ForegroundColor Green
        
        Write-Host "[3/6] 禁用IPv6..." -ForegroundColor Cyan
        Disable-NetAdapterBinding -Name "*" -ComponentID ms_tcpip6 -ErrorAction SilentlyContinue
        Write-Host "      ✓ IPv6已禁用" -ForegroundColor Green
        
        Write-Host "[4/6] 优化MTU设置..." -ForegroundColor Cyan
        $adapters = Get-NetAdapter | Where-Object {$_.Status -eq "Up"}
        foreach ($adapter in $adapters) {
            netsh interface ipv4 set subinterface $adapter.Name mtu=1200 store=persistent 2>$null | Out-Null
        }
        Write-Host "      ✓ MTU设置已优化" -ForegroundColor Green
        
        Write-Host "[5/6] 优化VPN TCP设置..." -ForegroundColor Cyan
        netsh int tcp set global autotuninglevel=disabled | Out-Null
        netsh int tcp set global ecncapability=disabled | Out-Null
        Write-Host "      ✓ VPN TCP设置已优化" -ForegroundColor Green
        
        Write-Host "[6/6] 配置VPN DNS..." -ForegroundColor Cyan
        $adapters = Get-NetAdapter | Where-Object {$_.Status -eq "Up"}
        foreach ($adapter in $adapters) {
            try {
                Set-DnsClientServerAddress -InterfaceAlias $adapter.Name -ServerAddresses @("*******", "*******", "*******") -ErrorAction SilentlyContinue
            } catch {
                # 忽略错误
            }
        }
        Write-Host "      ✓ VPN DNS已配置" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "================================================================" -ForegroundColor Green
        Write-Host "                    VPN优化完成！" -ForegroundColor Green
        Write-Host "          建议重启计算机后再连接VPN" -ForegroundColor Yellow
        Write-Host "================================================================" -ForegroundColor Green
        
    } catch {
        Write-Host "      ✗ VPN优化过程中出现错误: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 软路由优化函数  
function Start-RouterOptimization {
    Write-Host "开始软路由环境优化..." -ForegroundColor Green
    Write-Host ""
    
    try {
        Write-Host "[1/4] 检测软路由IP..." -ForegroundColor Cyan
        $defaultRoute = Get-NetRoute -DestinationPrefix "0.0.0.0/0" | Sort-Object RouteMetric | Select-Object -First 1
        $routerIP = $defaultRoute.NextHop
        Write-Host "      ✓ 检测到软路由IP: $routerIP" -ForegroundColor Green
        
        Write-Host "[2/4] 清理ARP表..." -ForegroundColor Cyan
        arp -d * 2>$null | Out-Null
        Write-Host "      ✓ ARP表已清理" -ForegroundColor Green
        
        Write-Host "[3/4] 优化路由表..." -ForegroundColor Cyan
        route delete 0.0.0.0 2>$null | Out-Null
        route add 0.0.0.0 mask 0.0.0.0 $routerIP metric 1 2>$null | Out-Null
        Write-Host "      ✓ 路由表已优化" -ForegroundColor Green
        
        Write-Host "[4/4] 测试软路由连接..." -ForegroundColor Cyan
        $pingResult = Test-Connection -ComputerName $routerIP -Count 1 -Quiet
        if ($pingResult) {
            Write-Host "      ✓ 软路由连接正常" -ForegroundColor Green
        } else {
            Write-Host "      ⚠ 软路由连接可能存在问题" -ForegroundColor Yellow
        }
        
        Write-Host ""
        Write-Host "================================================================" -ForegroundColor Green
        Write-Host "                  软路由优化完成！" -ForegroundColor Green
        Write-Host "================================================================" -ForegroundColor Green
        
    } catch {
        Write-Host "      ✗ 软路由优化过程中出现错误: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 网络诊断函数
function Start-NetworkDiagnosis {
    Write-Host "开始网络诊断..." -ForegroundColor Green
    Write-Host ""
    
    try {
        Write-Host "网络适配器状态：" -ForegroundColor Cyan
        Get-NetAdapter | Where-Object {$_.Status -eq "Up"} | Format-Table Name, InterfaceDescription, LinkSpeed -AutoSize
        
        Write-Host "当前IP配置：" -ForegroundColor Cyan
        Get-NetIPConfiguration | Where-Object {$_.NetAdapter.Status -eq "Up"} | Format-Table InterfaceAlias, IPv4Address, IPv4DefaultGateway -AutoSize
        
        Write-Host "DNS配置：" -ForegroundColor Cyan
        Get-DnsClientServerAddress | Where-Object {$_.ServerAddresses} | Format-Table InterfaceAlias, ServerAddresses -AutoSize
        
        Write-Host "连接测试：" -ForegroundColor Cyan
        $internetTest = Test-Connection -ComputerName "*******" -Count 1 -Quiet
        if ($internetTest) {
            Write-Host "✓ 互联网连接正常" -ForegroundColor Green
        } else {
            Write-Host "✗ 互联网连接异常" -ForegroundColor Red
        }
        
        Write-Host ""
        Write-Host "================================================================" -ForegroundColor Green
        Write-Host "                    诊断完成！" -ForegroundColor Green
        Write-Host "================================================================" -ForegroundColor Green
        
    } catch {
        Write-Host "✗ 诊断过程中出现错误: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 主循环
do {
    Show-Menu
    $choice = Read-Host "请输入选项 (0-5)"
    
    switch ($choice) {
        "1" { 
            Clear-Host
            Start-BasicOptimization
            Write-Host ""
            Pause-Script
        }
        "2" { 
            Clear-Host
            Start-VPNOptimization
            Write-Host ""
            Pause-Script
        }
        "3" { 
            Clear-Host
            Start-RouterOptimization
            Write-Host ""
            Pause-Script
        }
        "4" { 
            Clear-Host
            Write-Host "执行完整优化..." -ForegroundColor Green
            Start-BasicOptimization
            Write-Host ""
            Start-VPNOptimization  
            Write-Host ""
            Start-RouterOptimization
            Write-Host ""
            Write-Host "================================================================" -ForegroundColor Green
            Write-Host "                   完整优化完成！" -ForegroundColor Green
            Write-Host "              建议立即重启计算机" -ForegroundColor Yellow
            Write-Host "================================================================" -ForegroundColor Green
            Write-Host ""
            Pause-Script
        }
        "5" { 
            Clear-Host
            Start-NetworkDiagnosis
            Write-Host ""
            Pause-Script
        }
        "0" { 
            Write-Host ""
            Write-Host "感谢使用网络优化工具！" -ForegroundColor Green
            Write-Host ""
            break
        }
        default { 
            Write-Host "无效选项，请重新选择" -ForegroundColor Red
            Start-Sleep -Seconds 1
        }
    }
} while ($choice -ne "0")

# 最终暂停，确保用户能看到结果
Pause-Script "按任意键退出..."

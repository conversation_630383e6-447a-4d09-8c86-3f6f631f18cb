@echo off
chcp 65001 >nul
title 网络优化工具 - 快客VPN + 软路由优化
color 0A

echo ========================================
echo           网络优化工具 v1.0
echo     适用于快客VPN + 软路由环境
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [错误] 请以管理员身份运行此脚本！
    pause
    exit /b 1
)

echo [信息] 正在进行网络优化...
echo.

:: 1. 清理DNS缓存
echo [1/10] 清理DNS缓存...
ipconfig /flushdns >nul 2>&1
echo       ✓ DNS缓存已清理

:: 2. 重置网络堆栈
echo [2/10] 重置网络堆栈...
netsh winsock reset >nul 2>&1
netsh int ip reset >nul 2>&1
echo       ✓ 网络堆栈已重置

:: 3. 优化TCP/IP参数
echo [3/10] 优化TCP/IP参数...
netsh int tcp set global autotuninglevel=normal >nul 2>&1
netsh int tcp set global chimney=enabled >nul 2>&1
netsh int tcp set global rss=enabled >nul 2>&1
netsh int tcp set global netdma=enabled >nul 2>&1
echo       ✓ TCP/IP参数已优化

:: 4. 设置DNS服务器（针对VPN环境）
echo [4/10] 配置DNS服务器...
:: 获取活动网络接口
for /f "tokens=3" %%i in ('netsh interface show interface ^| findstr "已连接"') do (
    netsh interface ip set dns "%%i" static ******* primary >nul 2>&1
    netsh interface ip add dns "%%i" ******* index=2 >nul 2>&1
    netsh interface ip add dns "%%i" ******* index=3 >nul 2>&1
)
echo       ✓ DNS服务器已配置

:: 5. 优化网络适配器设置
echo [5/10] 优化网络适配器...
:: 禁用IPv6（可能与VPN冲突）
netsh interface ipv6 set global randomizeidentifiers=disabled >nul 2>&1
netsh interface ipv6 set privacy state=disabled >nul 2>&1
echo       ✓ 网络适配器已优化

:: 6. 清理ARP表
echo [6/10] 清理ARP表...
arp -d * >nul 2>&1
echo       ✓ ARP表已清理

:: 7. 优化路由表（软路由环境）
echo [7/10] 优化路由表...
:: 删除可能的冲突路由
route delete 0.0.0.0 >nul 2>&1
:: 添加默认路由（根据实际网关调整）
for /f "tokens=3" %%g in ('route print ^| findstr "0.0.0.0.*0.0.0.0"') do (
    route add 0.0.0.0 mask 0.0.0.0 %%g metric 1 >nul 2>&1
)
echo       ✓ 路由表已优化

:: 8. 重启网络服务
echo [8/10] 重启网络服务...
net stop "DHCP Client" >nul 2>&1
net start "DHCP Client" >nul 2>&1
net stop "DNS Client" >nul 2>&1
net start "DNS Client" >nul 2>&1
echo       ✓ 网络服务已重启

:: 9. 优化网络缓冲区
echo [9/10] 优化网络缓冲区...
netsh int tcp set global rsc=enabled >nul 2>&1
netsh int tcp set global ecncapability=enabled >nul 2>&1
echo       ✓ 网络缓冲区已优化

:: 10. 测试网络连接
echo [10/10] 测试网络连接...
ping -n 1 ******* >nul 2>&1
if %errorLevel% equ 0 (
    echo       ✓ 网络连接正常
) else (
    echo       ⚠ 网络连接可能存在问题
)

echo.
echo ========================================
echo           优化完成！
echo ========================================
echo.
echo [提示] 建议重启计算机以使所有更改生效
echo [提示] 如果使用VPN，请在重启后重新连接
echo.

:: 显示当前网络状态
echo 当前网络适配器状态：
netsh interface show interface
echo.

echo 当前DNS配置：
ipconfig /all | findstr "DNS"
echo.

pause

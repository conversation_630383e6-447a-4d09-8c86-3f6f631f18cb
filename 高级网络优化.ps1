# 高级网络优化脚本 - 快客VPN + 软路由专用
# 需要管理员权限运行

param(
    [switch]$VPNMode = $false,
    [switch]$SoftRouterMode = $false,
    [switch]$FullOptimization = $true
)

# 设置控制台编码
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "========================================" -ForegroundColor Green
Write-Host "        高级网络优化工具 v2.0" -ForegroundColor Green
Write-Host "    快客VPN + 软路由环境专用优化" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "[错误] 请以管理员身份运行PowerShell！" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 函数：显示进度
function Show-Progress {
    param($Step, $Total, $Description)
    $Percent = [math]::Round(($Step / $Total) * 100)
    Write-Host "[$Step/$Total] $Description..." -ForegroundColor Cyan
}

# 函数：优化网络适配器
function Optimize-NetworkAdapters {
    Write-Host "正在优化网络适配器设置..." -ForegroundColor Yellow
    
    # 获取所有网络适配器
    $adapters = Get-NetAdapter | Where-Object {$_.Status -eq "Up"}
    
    foreach ($adapter in $adapters) {
        try {
            # 优化接收缓冲区
            Set-NetAdapterAdvancedProperty -Name $adapter.Name -DisplayName "Receive Buffers" -DisplayValue "2048" -ErrorAction SilentlyContinue
            
            # 优化发送缓冲区
            Set-NetAdapterAdvancedProperty -Name $adapter.Name -DisplayName "Transmit Buffers" -DisplayValue "2048" -ErrorAction SilentlyContinue
            
            # 启用RSS（接收端缩放）
            Set-NetAdapterRss -Name $adapter.Name -Enabled $true -ErrorAction SilentlyContinue
            
            # 优化中断调节
            Set-NetAdapterAdvancedProperty -Name $adapter.Name -DisplayName "Interrupt Moderation" -DisplayValue "Enabled" -ErrorAction SilentlyContinue
            
            Write-Host "  ✓ 已优化适配器: $($adapter.Name)" -ForegroundColor Green
        }
        catch {
            Write-Host "  ⚠ 跳过适配器: $($adapter.Name) (不支持某些设置)" -ForegroundColor Yellow
        }
    }
}

# 函数：优化TCP设置
function Optimize-TCPSettings {
    Write-Host "正在优化TCP/IP设置..." -ForegroundColor Yellow
    
    # TCP全局设置
    netsh int tcp set global autotuninglevel=normal | Out-Null
    netsh int tcp set global chimney=enabled | Out-Null
    netsh int tcp set global rss=enabled | Out-Null
    netsh int tcp set global netdma=enabled | Out-Null
    netsh int tcp set global ecncapability=enabled | Out-Null
    netsh int tcp set global rsc=enabled | Out-Null
    
    # 设置TCP窗口缩放
    netsh int tcp set global autotuninglevel=normal | Out-Null
    
    # 优化TCP Chimney
    netsh int tcp set global chimney=enabled | Out-Null
    
    Write-Host "  ✓ TCP/IP设置已优化" -ForegroundColor Green
}

# 函数：配置DNS（VPN友好）
function Configure-DNS {
    Write-Host "正在配置DNS设置..." -ForegroundColor Yellow
    
    # 清理DNS缓存
    Clear-DnsClientCache
    
    # 设置DNS服务器（多个备选）
    $dnsServers = @("*******", "*******", "*******", "*******")
    
    $adapters = Get-NetAdapter | Where-Object {$_.Status -eq "Up" -and $_.Name -notlike "*VMware*" -and $_.Name -notlike "*VirtualBox*"}
    
    foreach ($adapter in $adapters) {
        try {
            Set-DnsClientServerAddress -InterfaceAlias $adapter.Name -ServerAddresses $dnsServers -ErrorAction SilentlyContinue
            Write-Host "  ✓ 已配置DNS: $($adapter.Name)" -ForegroundColor Green
        }
        catch {
            Write-Host "  ⚠ 跳过DNS配置: $($adapter.Name)" -ForegroundColor Yellow
        }
    }
}

# 函数：优化路由表（软路由环境）
function Optimize-RoutingTable {
    Write-Host "正在优化路由表..." -ForegroundColor Yellow
    
    # 清理持久路由
    route delete 0.0.0.0 2>$null | Out-Null
    
    # 获取默认网关
    $defaultGateway = (Get-NetRoute -DestinationPrefix "0.0.0.0/0" | Sort-Object RouteMetric | Select-Object -First 1).NextHop
    
    if ($defaultGateway) {
        # 添加优化的默认路由
        route add 0.0.0.0 mask 0.0.0.0 $defaultGateway metric 1 -p 2>$null | Out-Null
        Write-Host "  ✓ 路由表已优化，默认网关: $defaultGateway" -ForegroundColor Green
    }
}

# 函数：VPN特定优化
function Optimize-VPN {
    Write-Host "正在进行VPN特定优化..." -ForegroundColor Yellow
    
    # 禁用IPv6（避免VPN泄漏）
    Disable-NetAdapterBinding -Name "*" -ComponentID ms_tcpip6 -ErrorAction SilentlyContinue
    
    # 优化MTU设置
    $adapters = Get-NetAdapter | Where-Object {$_.Status -eq "Up"}
    foreach ($adapter in $adapters) {
        if ($adapter.Name -like "*TAP*" -or $adapter.Name -like "*VPN*") {
            netsh interface ipv4 set subinterface $adapter.Name mtu=1200 store=persistent 2>$null | Out-Null
            Write-Host "  ✓ 已优化VPN适配器MTU: $($adapter.Name)" -ForegroundColor Green
        }
    }
    
    # 设置VPN友好的DNS
    Set-DnsClientGlobalSetting -SuffixSearchList @() -ErrorAction SilentlyContinue
}

# 主优化流程
try {
    Show-Progress 1 10 "清理网络缓存"
    Clear-DnsClientCache
    netsh winsock reset | Out-Null
    netsh int ip reset | Out-Null
    
    Show-Progress 2 10 "优化TCP/IP设置"
    Optimize-TCPSettings
    
    Show-Progress 3 10 "优化网络适配器"
    Optimize-NetworkAdapters
    
    Show-Progress 4 10 "配置DNS服务器"
    Configure-DNS
    
    Show-Progress 5 10 "优化路由表"
    Optimize-RoutingTable
    
    if ($VPNMode -or $FullOptimization) {
        Show-Progress 6 10 "VPN特定优化"
        Optimize-VPN
    }
    
    Show-Progress 7 10 "重启网络服务"
    Restart-Service -Name "Dhcp" -Force -ErrorAction SilentlyContinue
    Restart-Service -Name "Dnscache" -Force -ErrorAction SilentlyContinue
    
    Show-Progress 8 10 "清理ARP表"
    arp -d * 2>$null | Out-Null
    
    Show-Progress 9 10 "应用注册表优化"
    # TCP/IP注册表优化
    $regPath = "HKLM:\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters"
    Set-ItemProperty -Path $regPath -Name "TcpAckFrequency" -Value 1 -ErrorAction SilentlyContinue
    Set-ItemProperty -Path $regPath -Name "TCPNoDelay" -Value 1 -ErrorAction SilentlyContinue
    Set-ItemProperty -Path $regPath -Name "TcpDelAckTicks" -Value 0 -ErrorAction SilentlyContinue
    
    Show-Progress 10 10 "测试网络连接"
    $pingResult = Test-NetConnection -ComputerName "*******" -Port 53 -InformationLevel Quiet
    
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "           优化完成！" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    
    if ($pingResult) {
        Write-Host "✓ 网络连接测试成功" -ForegroundColor Green
    } else {
        Write-Host "⚠ 网络连接测试失败，请检查网络设置" -ForegroundColor Yellow
    }
    
    # 显示当前网络状态
    Write-Host ""
    Write-Host "当前活动网络适配器：" -ForegroundColor Cyan
    Get-NetAdapter | Where-Object {$_.Status -eq "Up"} | Format-Table Name, InterfaceDescription, LinkSpeed -AutoSize
    
    Write-Host ""
    Write-Host "当前DNS配置：" -ForegroundColor Cyan
    Get-DnsClientServerAddress | Where-Object {$_.ServerAddresses} | Format-Table InterfaceAlias, ServerAddresses -AutoSize
    
    Write-Host ""
    Write-Host "[建议] 重启计算机以使所有更改完全生效" -ForegroundColor Yellow
    Write-Host "[提示] 如果使用VPN，请在重启后重新连接" -ForegroundColor Yellow
    
} catch {
    Write-Host "优化过程中出现错误: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Read-Host "按回车键退出"

# 软路由环境网络优化脚本
# 专门针对软路由 + VPN环境设计

param(
    [string]$SoftRouterIP = "",
    [switch]$AutoDetect = $true,
    [switch]$VerboseOutput = $false
)

# 设置控制台
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$Host.UI.RawUI.WindowTitle = "软路由环境优化工具"

Write-Host "========================================" -ForegroundColor Magenta
Write-Host "        软路由环境优化工具 v1.0" -ForegroundColor Magenta
Write-Host "    适用于OpenWrt/LEDE等软路由系统" -ForegroundColor Magenta
Write-Host "========================================" -ForegroundColor Magenta
Write-Host ""

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "[错误] 需要管理员权限！请以管理员身份运行PowerShell" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

# 函数：自动检测软路由IP
function Get-SoftRouterIP {
    Write-Host "正在自动检测软路由IP地址..." -ForegroundColor Yellow
    
    # 获取默认网关
    $gateways = Get-NetRoute -DestinationPrefix "0.0.0.0/0" | Where-Object {$_.NextHop -ne "0.0.0.0"} | Sort-Object RouteMetric
    
    foreach ($gateway in $gateways) {
        $ip = $gateway.NextHop
        Write-Host "检测到网关: $ip" -ForegroundColor Cyan
        
        # 尝试ping测试
        if (Test-Connection -ComputerName $ip -Count 1 -Quiet) {
            # 尝试检测是否为软路由（通常开放80或443端口）
            $webTest = Test-NetConnection -ComputerName $ip -Port 80 -InformationLevel Quiet -WarningAction SilentlyContinue
            if ($webTest) {
                Write-Host "✓ 检测到软路由: $ip" -ForegroundColor Green
                return $ip
            }
        }
    }
    
    Write-Host "⚠ 未能自动检测到软路由，请手动指定IP" -ForegroundColor Yellow
    return $null
}

# 函数：优化软路由连接
function Optimize-SoftRouterConnection {
    param($RouterIP)
    
    Write-Host "正在优化与软路由的连接..." -ForegroundColor Yellow
    
    # 清理ARP缓存
    arp -d * 2>$null | Out-Null
    
    # 添加静态ARP条目（如果可能）
    try {
        $macAddress = (arp -a $RouterIP | Select-String $RouterIP).ToString().Split()[1]
        if ($macAddress -and $macAddress -match "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$") {
            arp -s $RouterIP $macAddress 2>$null | Out-Null
            Write-Host "  ✓ 已添加静态ARP条目: $RouterIP -> $macAddress" -ForegroundColor Green
        }
    }
    catch {
        Write-Host "  ⚠ 无法添加静态ARP条目" -ForegroundColor Yellow
    }
    
    # 优化到软路由的路由
    route delete $RouterIP 2>$null | Out-Null
    route add $RouterIP mask *************** $RouterIP metric 1 2>$null | Out-Null
    
    Write-Host "  ✓ 软路由连接已优化" -ForegroundColor Green
}

# 函数：优化网络适配器（软路由环境）
function Optimize-AdaptersForSoftRouter {
    Write-Host "正在优化网络适配器（软路由环境）..." -ForegroundColor Yellow
    
    $adapters = Get-NetAdapter | Where-Object {$_.Status -eq "Up" -and $_.Name -notlike "*VMware*" -and $_.Name -notlike "*VirtualBox*"}
    
    foreach ($adapter in $adapters) {
        try {
            # 禁用节能模式
            $powerMgmt = Get-WmiObject -Class Win32_NetworkAdapter | Where-Object {$_.NetConnectionID -eq $adapter.Name}
            if ($powerMgmt) {
                $powerMgmt.SetPowerState(1) | Out-Null
            }
            
            # 设置适合软路由的缓冲区大小
            Set-NetAdapterAdvancedProperty -Name $adapter.Name -DisplayName "Receive Buffers" -DisplayValue "1024" -ErrorAction SilentlyContinue
            Set-NetAdapterAdvancedProperty -Name $adapter.Name -DisplayName "Transmit Buffers" -DisplayValue "1024" -ErrorAction SilentlyContinue
            
            # 启用流控制
            Set-NetAdapterAdvancedProperty -Name $adapter.Name -DisplayName "Flow Control" -DisplayValue "Enabled" -ErrorAction SilentlyContinue
            
            # 优化中断设置
            Set-NetAdapterAdvancedProperty -Name $adapter.Name -DisplayName "Interrupt Moderation" -DisplayValue "Enabled" -ErrorAction SilentlyContinue
            
            Write-Host "  ✓ 已优化适配器: $($adapter.Name)" -ForegroundColor Green
        }
        catch {
            Write-Host "  ⚠ 跳过适配器: $($adapter.Name)" -ForegroundColor Yellow
        }
    }
}

# 函数：配置软路由DNS
function Configure-SoftRouterDNS {
    param($RouterIP)
    
    Write-Host "正在配置软路由DNS..." -ForegroundColor Yellow
    
    # 软路由环境推荐的DNS配置
    $dnsServers = @($RouterIP, "*******", "*******", "*******")
    
    $adapters = Get-NetAdapter | Where-Object {$_.Status -eq "Up" -and $_.Name -notlike "*VMware*"}
    
    foreach ($adapter in $adapters) {
        try {
            Set-DnsClientServerAddress -InterfaceAlias $adapter.Name -ServerAddresses $dnsServers -ErrorAction SilentlyContinue
            Write-Host "  ✓ 已配置DNS: $($adapter.Name)" -ForegroundColor Green
        }
        catch {
            Write-Host "  ⚠ DNS配置失败: $($adapter.Name)" -ForegroundColor Yellow
        }
    }
}

# 函数：优化软路由TCP设置
function Optimize-TCPForSoftRouter {
    Write-Host "正在优化TCP设置（软路由环境）..." -ForegroundColor Yellow
    
    # 软路由环境的TCP优化
    netsh int tcp set global autotuninglevel=normal | Out-Null
    netsh int tcp set global chimney=enabled | Out-Null
    netsh int tcp set global rss=enabled | Out-Null
    netsh int tcp set global netdma=enabled | Out-Null
    
    # 设置较小的TCP窗口以适应软路由
    netsh int tcp set global initialrto=1000 | Out-Null
    
    # 优化拥塞控制
    netsh int tcp set global ecncapability=enabled | Out-Null
    
    Write-Host "  ✓ TCP设置已优化" -ForegroundColor Green
}

# 主执行流程
try {
    # 自动检测或使用指定的软路由IP
    if ($AutoDetect -and [string]::IsNullOrEmpty($SoftRouterIP)) {
        $SoftRouterIP = Get-SoftRouterIP
    }
    
    if ([string]::IsNullOrEmpty($SoftRouterIP)) {
        $SoftRouterIP = Read-Host "请输入软路由IP地址（例如：***********）"
    }
    
    if ([string]::IsNullOrEmpty($SoftRouterIP)) {
        Write-Host "错误：未指定软路由IP地址" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "使用软路由IP: $SoftRouterIP" -ForegroundColor Cyan
    Write-Host ""
    
    # 执行优化步骤
    Write-Host "[1/8] 清理网络缓存..." -ForegroundColor Cyan
    Clear-DnsClientCache
    netsh winsock reset | Out-Null
    
    Write-Host "[2/8] 优化软路由连接..." -ForegroundColor Cyan
    Optimize-SoftRouterConnection -RouterIP $SoftRouterIP
    
    Write-Host "[3/8] 优化网络适配器..." -ForegroundColor Cyan
    Optimize-AdaptersForSoftRouter
    
    Write-Host "[4/8] 配置DNS设置..." -ForegroundColor Cyan
    Configure-SoftRouterDNS -RouterIP $SoftRouterIP
    
    Write-Host "[5/8] 优化TCP设置..." -ForegroundColor Cyan
    Optimize-TCPForSoftRouter
    
    Write-Host "[6/8] 优化路由表..." -ForegroundColor Cyan
    # 清理并重建路由表
    route delete 0.0.0.0 2>$null | Out-Null
    route add 0.0.0.0 mask 0.0.0.0 $SoftRouterIP metric 1 2>$null | Out-Null
    
    Write-Host "[7/8] 重启网络服务..." -ForegroundColor Cyan
    Restart-Service -Name "Dhcp" -Force -ErrorAction SilentlyContinue
    Restart-Service -Name "Dnscache" -Force -ErrorAction SilentlyContinue
    
    Write-Host "[8/8] 测试连接..." -ForegroundColor Cyan
    $pingTest = Test-Connection -ComputerName $SoftRouterIP -Count 2 -Quiet
    $internetTest = Test-Connection -ComputerName "*******" -Count 1 -Quiet
    
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Magenta
    Write-Host "           优化完成！" -ForegroundColor Magenta
    Write-Host "========================================" -ForegroundColor Magenta
    Write-Host ""
    
    # 显示测试结果
    if ($pingTest) {
        Write-Host "✓ 软路由连接正常 ($SoftRouterIP)" -ForegroundColor Green
    } else {
        Write-Host "✗ 软路由连接异常 ($SoftRouterIP)" -ForegroundColor Red
    }
    
    if ($internetTest) {
        Write-Host "✓ 互联网连接正常" -ForegroundColor Green
    } else {
        Write-Host "✗ 互联网连接异常" -ForegroundColor Red
    }
    
    # 显示当前网络状态
    Write-Host ""
    Write-Host "当前网络配置：" -ForegroundColor Cyan
    Write-Host "软路由IP: $SoftRouterIP" -ForegroundColor White
    
    $currentDNS = Get-DnsClientServerAddress | Where-Object {$_.ServerAddresses} | Select-Object -First 1
    if ($currentDNS) {
        Write-Host "DNS服务器: $($currentDNS.ServerAddresses -join ', ')" -ForegroundColor White
    }
    
    $defaultRoute = Get-NetRoute -DestinationPrefix "0.0.0.0/0" | Sort-Object RouteMetric | Select-Object -First 1
    if ($defaultRoute) {
        Write-Host "默认网关: $($defaultRoute.NextHop)" -ForegroundColor White
    }
    
    Write-Host ""
    Write-Host "[提示] 如果使用VPN，建议在软路由上配置而不是在客户端" -ForegroundColor Yellow
    Write-Host "[提示] 重启计算机以确保所有设置完全生效" -ForegroundColor Yellow
    
} catch {
    Write-Host "优化过程中出现错误: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Read-Host "按回车键退出"

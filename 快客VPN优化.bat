@echo off
chcp 65001 >nul
title 快客VPN专用网络优化工具
color 0B

echo ========================================
echo        快客VPN专用优化工具 v1.0
echo      解决VPN连接和速度问题
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [错误] 请以管理员身份运行此脚本！
    echo 右键点击脚本 → 以管理员身份运行
    pause
    exit /b 1
)

echo [信息] 正在为快客VPN优化网络环境...
echo.

:: 1. 停止可能冲突的服务
echo [1/12] 停止冲突服务...
sc stop "WinHttpAutoProxySvc" >nul 2>&1
sc stop "WebClient" >nul 2>&1
echo       ✓ 冲突服务已停止

:: 2. 清理网络配置
echo [2/12] 清理网络配置...
ipconfig /release >nul 2>&1
ipconfig /flushdns >nul 2>&1
ipconfig /renew >nul 2>&1
echo       ✓ 网络配置已清理

:: 3. 重置网络组件
echo [3/12] 重置网络组件...
netsh winsock reset >nul 2>&1
netsh int ip reset >nul 2>&1
netsh advfirewall reset >nul 2>&1
echo       ✓ 网络组件已重置

:: 4. 优化VPN相关的TCP设置
echo [4/12] 优化VPN TCP设置...
netsh int tcp set global autotuninglevel=disabled >nul 2>&1
netsh int tcp set global chimney=disabled >nul 2>&1
netsh int tcp set global rss=disabled >nul 2>&1
netsh int tcp set global netdma=disabled >nul 2>&1
netsh int tcp set global ecncapability=disabled >nul 2>&1
echo       ✓ VPN TCP设置已优化

:: 5. 配置VPN友好的DNS
echo [5/12] 配置VPN DNS...
:: 设置主要DNS为快客推荐的DNS
netsh interface ip set dns "本地连接" static ******* primary >nul 2>&1
netsh interface ip add dns "本地连接" ******* index=2 >nul 2>&1
netsh interface ip set dns "以太网" static ******* primary >nul 2>&1
netsh interface ip add dns "以太网" ******* index=2 >nul 2>&1
netsh interface ip set dns "WLAN" static ******* primary >nul 2>&1
netsh interface ip add dns "WLAN" ******* index=2 >nul 2>&1
echo       ✓ VPN DNS已配置

:: 6. 禁用IPv6（避免VPN泄漏）
echo [6/12] 禁用IPv6...
netsh interface ipv6 set global randomizeidentifiers=disabled >nul 2>&1
netsh interface ipv6 set privacy state=disabled >nul 2>&1
reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\Tcpip6\Parameters" /v DisabledComponents /t REG_DWORD /d 255 /f >nul 2>&1
echo       ✓ IPv6已禁用

:: 7. 优化MTU设置（重要！）
echo [7/12] 优化MTU设置...
:: 设置较小的MTU以适应VPN隧道
netsh interface ipv4 set subinterface "本地连接" mtu=1200 store=persistent >nul 2>&1
netsh interface ipv4 set subinterface "以太网" mtu=1200 store=persistent >nul 2>&1
netsh interface ipv4 set subinterface "WLAN" mtu=1200 store=persistent >nul 2>&1
echo       ✓ MTU设置已优化

:: 8. 清理路由表
echo [8/12] 清理路由表...
route delete 0.0.0.0 >nul 2>&1
:: 等待网络重新获取路由
timeout /t 3 >nul
echo       ✓ 路由表已清理

:: 9. 优化注册表设置
echo [9/12] 优化注册表...
:: TCP/IP性能优化
reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters" /v TcpAckFrequency /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters" /v TCPNoDelay /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters" /v TcpDelAckTicks /t REG_DWORD /d 0 /f >nul 2>&1

:: VPN优化设置
reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters" /v DefaultTTL /t REG_DWORD /d 64 /f >nul 2>&1
reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters" /v EnablePMTUDiscovery /t REG_DWORD /d 0 /f >nul 2>&1
echo       ✓ 注册表已优化

:: 10. 重启关键网络服务
echo [10/12] 重启网络服务...
net stop "DHCP Client" >nul 2>&1
net start "DHCP Client" >nul 2>&1
net stop "DNS Client" >nul 2>&1
net start "DNS Client" >nul 2>&1
net stop "Network Location Awareness" >nul 2>&1
net start "Network Location Awareness" >nul 2>&1
echo       ✓ 网络服务已重启

:: 11. 清理网络缓存
echo [11/12] 清理网络缓存...
arp -d * >nul 2>&1
nbtstat -R >nul 2>&1
nbtstat -RR >nul 2>&1
echo       ✓ 网络缓存已清理

:: 12. 测试网络连接
echo [12/12] 测试网络连接...
ping -n 1 ******* >nul 2>&1
if %errorLevel% equ 0 (
    echo       ✓ 基础网络连接正常
) else (
    echo       ⚠ 基础网络连接异常，请检查网络
)

:: 测试DNS解析
nslookup google.com >nul 2>&1
if %errorLevel% equ 0 (
    echo       ✓ DNS解析正常
) else (
    echo       ⚠ DNS解析可能存在问题
)

echo.
echo ========================================
echo          快客VPN优化完成！
echo ========================================
echo.
echo [重要提示]
echo 1. 请重启计算机以使所有更改生效
echo 2. 重启后再连接快客VPN
echo 3. 如果VPN仍然不稳定，请尝试以下操作：
echo    - 更换VPN服务器节点
echo    - 检查防火墙设置
echo    - 确保快客VPN客户端是最新版本
echo.
echo [网络状态检查]
echo 当前网络适配器：
netsh interface show interface | findstr "已连接"
echo.
echo 当前DNS设置：
ipconfig /all | findstr "DNS 服务器"
echo.

echo 按任意键退出...
pause >nul

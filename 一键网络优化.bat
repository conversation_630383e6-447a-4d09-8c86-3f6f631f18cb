@echo off
:: 设置代码页为UTF-8，支持中文显示
chcp 65001 >nul 2>&1

:: 设置窗口标题和颜色
title 一键网络优化工具 - 快客VPN + 软路由专用
color 0A
mode con cols=80 lines=40

:: 显示欢迎界面
cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                          一键网络优化工具 v2.0                              ║
echo ║                      快客VPN + 软路由环境专用                               ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

:: 检查管理员权限
echo [检查] 正在验证管理员权限...
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo.
    echo ╔══════════════════════════════════════════════════════════════════════════════╗
    echo ║                              权限不足！                                     ║
    echo ╠══════════════════════════════════════════════════════════════════════════════╣
    echo ║  请按以下步骤重新运行：                                                      ║
    echo ║  1. 右键点击此脚本文件                                                      ║
    echo ║  2. 选择"以管理员身份运行"                                                  ║
    echo ║  3. 在弹出的UAC对话框中点击"是"                                            ║
    echo ╚══════════════════════════════════════════════════════════════════════════════╝
    echo.
    echo 按任意键退出...
    pause >nul
    exit /b 1
)
echo [成功] ✓ 管理员权限验证通过
echo.

:: 显示菜单
:MENU
cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                          选择优化模式                                       ║
echo ╠══════════════════════════════════════════════════════════════════════════════╣
echo ║  [1] 快速优化 - 基础网络清理和优化 (推荐新手)                              ║
echo ║  [2] VPN优化 - 专门解决快客VPN连接问题                                      ║
echo ║  [3] 软路由优化 - 优化软路由环境连接                                        ║
echo ║  [4] 完整优化 - 执行所有优化项目 (推荐)                                     ║
echo ║  [5] 网络诊断 - 检查当前网络状态                                            ║
echo ║  [0] 退出程序                                                                ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
set /p choice="请输入选项 (0-5): "

if "%choice%"=="1" goto QUICK_OPT
if "%choice%"=="2" goto VPN_OPT
if "%choice%"=="3" goto ROUTER_OPT
if "%choice%"=="4" goto FULL_OPT
if "%choice%"=="5" goto DIAGNOSE
if "%choice%"=="0" goto EXIT
echo [错误] 无效选项，请重新选择
timeout /t 2 >nul
goto MENU

:: 快速优化
:QUICK_OPT
cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                            快速网络优化                                     ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo [1/6] 清理DNS缓存...
ipconfig /flushdns >nul 2>&1
if %errorLevel% equ 0 (
    echo       ✓ DNS缓存清理成功
) else (
    echo       ✗ DNS缓存清理失败
)

echo [2/6] 重置网络堆栈...
netsh winsock reset >nul 2>&1
netsh int ip reset >nul 2>&1
echo       ✓ 网络堆栈重置完成

echo [3/6] 优化TCP设置...
netsh int tcp set global autotuninglevel=normal >nul 2>&1
netsh int tcp set global chimney=enabled >nul 2>&1
netsh int tcp set global rss=enabled >nul 2>&1
echo       ✓ TCP设置优化完成

echo [4/6] 配置DNS服务器...
for /f "tokens=3" %%i in ('netsh interface show interface ^| findstr "已连接"') do (
    netsh interface ip set dns "%%i" static ******* primary >nul 2>&1
    netsh interface ip add dns "%%i" ******* index=2 >nul 2>&1
)
echo       ✓ DNS服务器配置完成

echo [5/6] 重启网络服务...
net stop "DHCP Client" >nul 2>&1
net start "DHCP Client" >nul 2>&1
net stop "DNS Client" >nul 2>&1
net start "DNS Client" >nul 2>&1
echo       ✓ 网络服务重启完成

echo [6/6] 测试网络连接...
ping -n 1 ******* >nul 2>&1
if %errorLevel% equ 0 (
    echo       ✓ 网络连接测试成功
) else (
    echo       ⚠ 网络连接测试失败
)

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                          快速优化完成！                                     ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
goto CONTINUE

:: VPN优化
:VPN_OPT
cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                          快客VPN专用优化                                    ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo [1/8] 停止冲突服务...
sc stop "WinHttpAutoProxySvc" >nul 2>&1
sc stop "WebClient" >nul 2>&1
echo       ✓ 冲突服务已停止

echo [2/8] 清理网络配置...
ipconfig /release >nul 2>&1
ipconfig /flushdns >nul 2>&1
ipconfig /renew >nul 2>&1
echo       ✓ 网络配置已清理

echo [3/8] 重置网络组件...
netsh winsock reset >nul 2>&1
netsh int ip reset >nul 2>&1
echo       ✓ 网络组件已重置

echo [4/8] 优化VPN TCP设置...
netsh int tcp set global autotuninglevel=disabled >nul 2>&1
netsh int tcp set global ecncapability=disabled >nul 2>&1
echo       ✓ VPN TCP设置已优化

echo [5/8] 配置VPN DNS...
netsh interface ip set dns "本地连接" static ******* primary >nul 2>&1
netsh interface ip set dns "以太网" static ******* primary >nul 2>&1
netsh interface ip set dns "WLAN" static ******* primary >nul 2>&1
echo       ✓ VPN DNS已配置

echo [6/8] 禁用IPv6...
reg add "HKLM\SYSTEM\CurrentControlSet\Services\Tcpip6\Parameters" /v DisabledComponents /t REG_DWORD /d 255 /f >nul 2>&1
echo       ✓ IPv6已禁用

echo [7/8] 优化MTU设置...
netsh interface ipv4 set subinterface "本地连接" mtu=1200 store=persistent >nul 2>&1
netsh interface ipv4 set subinterface "以太网" mtu=1200 store=persistent >nul 2>&1
echo       ✓ MTU设置已优化

echo [8/8] 重启网络服务...
net stop "DHCP Client" >nul 2>&1 & net start "DHCP Client" >nul 2>&1
net stop "DNS Client" >nul 2>&1 & net start "DNS Client" >nul 2>&1
echo       ✓ 网络服务已重启

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                        快客VPN优化完成！                                    ║
echo ║  提示：请重启计算机后再连接VPN以获得最佳效果                                ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
goto CONTINUE

:: 软路由优化
:ROUTER_OPT
cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                          软路由环境优化                                     ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo [1/6] 检测软路由IP...
for /f "tokens=3" %%g in ('route print ^| findstr "0.0.0.0.*0.0.0.0"') do (
    set ROUTER_IP=%%g
    echo       ✓ 检测到软路由IP: %%g
)

echo [2/6] 清理ARP表...
arp -d * >nul 2>&1
echo       ✓ ARP表已清理

echo [3/6] 优化路由表...
route delete 0.0.0.0 >nul 2>&1
if defined ROUTER_IP (
    route add 0.0.0.0 mask 0.0.0.0 %ROUTER_IP% metric 1 >nul 2>&1
    echo       ✓ 路由表已优化，网关: %ROUTER_IP%
) else (
    echo       ⚠ 未检测到软路由IP
)

echo [4/6] 配置软路由DNS...
if defined ROUTER_IP (
    for /f "tokens=3" %%i in ('netsh interface show interface ^| findstr "已连接"') do (
        netsh interface ip set dns "%%i" static %ROUTER_IP% primary >nul 2>&1
        netsh interface ip add dns "%%i" ******* index=2 >nul 2>&1
    )
    echo       ✓ 软路由DNS已配置
)

echo [5/6] 优化网络参数...
netsh int tcp set global autotuninglevel=normal >nul 2>&1
netsh int tcp set global chimney=enabled >nul 2>&1
echo       ✓ 网络参数已优化

echo [6/6] 测试软路由连接...
if defined ROUTER_IP (
    ping -n 1 %ROUTER_IP% >nul 2>&1
    if %errorLevel% equ 0 (
        echo       ✓ 软路由连接正常
    ) else (
        echo       ⚠ 软路由连接异常
    )
)

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                        软路由优化完成！                                     ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
goto CONTINUE

:: 完整优化
:FULL_OPT
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                          执行完整优化                                       ║
echo ║  这将依次执行：快速优化 → VPN优化 → 软路由优化                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo 开始完整优化，请稍候...
echo.

call :QUICK_OPT_SILENT
echo ✓ 快速优化完成
call :VPN_OPT_SILENT  
echo ✓ VPN优化完成
call :ROUTER_OPT_SILENT
echo ✓ 软路由优化完成

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                          完整优化完成！                                     ║
echo ║  建议立即重启计算机以使所有更改生效                                          ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
goto CONTINUE

:: 网络诊断
:DIAGNOSE
cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                          网络状态诊断                                       ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo [诊断] 网络适配器状态：
netsh interface show interface | findstr "已连接\|已断开"
echo.

echo [诊断] 当前IP配置：
ipconfig | findstr "IPv4\|默认网关\|子网掩码"
echo.

echo [诊断] DNS服务器：
ipconfig /all | findstr "DNS 服务器"
echo.

echo [诊断] 路由表（默认路由）：
route print | findstr "0.0.0.0.*0.0.0.0"
echo.

echo [诊断] 连接测试：
echo 测试本地网关...
for /f "tokens=3" %%g in ('route print ^| findstr "0.0.0.0.*0.0.0.0"') do (
    ping -n 1 %%g >nul 2>&1
    if %errorLevel% equ 0 (
        echo ✓ 网关连接正常: %%g
    ) else (
        echo ✗ 网关连接异常: %%g
    )
)

echo 测试DNS解析...
nslookup google.com >nul 2>&1
if %errorLevel% equ 0 (
    echo ✓ DNS解析正常
) else (
    echo ✗ DNS解析异常
)

echo 测试互联网连接...
ping -n 1 ******* >nul 2>&1
if %errorLevel% equ 0 (
    echo ✓ 互联网连接正常
) else (
    echo ✗ 互联网连接异常
)

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                          诊断完成                                           ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
goto CONTINUE

:: 静默执行函数（用于完整优化）
:QUICK_OPT_SILENT
ipconfig /flushdns >nul 2>&1
netsh winsock reset >nul 2>&1
netsh int ip reset >nul 2>&1
netsh int tcp set global autotuninglevel=normal >nul 2>&1
for /f "tokens=3" %%i in ('netsh interface show interface ^| findstr "已连接"') do (
    netsh interface ip set dns "%%i" static ******* primary >nul 2>&1
)
net stop "DHCP Client" >nul 2>&1 & net start "DHCP Client" >nul 2>&1
goto :eof

:VPN_OPT_SILENT
sc stop "WinHttpAutoProxySvc" >nul 2>&1
ipconfig /flushdns >nul 2>&1
netsh winsock reset >nul 2>&1
netsh int tcp set global autotuninglevel=disabled >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Services\Tcpip6\Parameters" /v DisabledComponents /t REG_DWORD /d 255 /f >nul 2>&1
netsh interface ipv4 set subinterface "以太网" mtu=1200 store=persistent >nul 2>&1
goto :eof

:ROUTER_OPT_SILENT
arp -d * >nul 2>&1
for /f "tokens=3" %%g in ('route print ^| findstr "0.0.0.0.*0.0.0.0"') do (
    route delete 0.0.0.0 >nul 2>&1
    route add 0.0.0.0 mask 0.0.0.0 %%g metric 1 >nul 2>&1
)
goto :eof

:: 继续选项
:CONTINUE
echo.
echo 选择下一步操作：
echo [1] 返回主菜单
echo [2] 立即重启计算机
echo [3] 退出程序
echo.
set /p next="请输入选项 (1-3): "

if "%next%"=="1" goto MENU
if "%next%"=="2" (
    echo 正在重启计算机...
    shutdown /r /t 5 /c "网络优化完成，系统将在5秒后重启"
    exit
)
if "%next%"=="3" goto EXIT
echo 无效选项，返回主菜单
timeout /t 2 >nul
goto MENU

:: 退出程序
:EXIT
cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                            感谢使用！                                       ║
echo ║                                                                              ║
echo ║  如果网络问题仍然存在，建议：                                                ║
echo ║  1. 重启计算机                                                               ║
echo ║  2. 检查VPN客户端设置                                                       ║
echo ║  3. 联系网络管理员                                                           ║
echo ║                                                                              ║
echo ║  脚本制作：网络优化专家                                                     ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo 按任意键退出...
pause >nul
exit
